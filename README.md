# SQL数据处理脚本
应将该脚本与要读取的sql文件位于同一目录
这个Python脚本用于处理目标文件夹下的SQL文件，根据tableStructure文件夹下的CSV文件提取对应表的INSERT和UPDATE操作数据，并保存到Excel文件中。

## 功能特性

- 自动读取tableStructure文件夹下的CSV文件，获取表结构信息
- 解析SQL文件中的INSERT和UPDATE操作
- 根据CSV文件中的字段名称，将数据按正确的字段顺序保存到Excel
- 支持处理多个SQL文件
- 为每个表生成独立的Excel文件



## 安装依赖

```bash
pip install -r requirements.txt
```

## 使用方法

1. 确保SQL文件位于当前目录
2. 确保tableStructure文件夹包含对应的CSV表结构文件
3. 运行脚本：

```bash
python sql_processor.py
```



## 输出说明

脚本会在output文件夹中为每个表生成一个Excel文件，文件名格式为：`{表名}_operations.xlsx`

Excel文件包含以下列：
- `操作类型`: INSERT 或 UPDATE
- 表的各个字段列（严格按照CSV文件中第一行的字段顺序）

**注意**:
- 如果output目录中已存在同名Excel文件，脚本会自动覆盖
- 字段顺序完全按照tableStructure中对应CSV文件的第一行定义
- 没有重复字段，每个字段只出现一次

## 支持的SQL格式

脚本支持MySQL binlog格式的SQL文件，能够解析如下格式的操作：

```sql
### INSERT INTO `database`.`table_name`
### SET
###   @1=value1
###   @2=value2
###   ...

### UPDATE `database`.`table_name`
### WHERE
###   @1=old_value1
###   @2=old_value2
### SET
###   @1=new_value1
###   @2=new_value2
###   ...
```

## 注意事项

- CSV文件的第一行应该是字段名
- 字段名可以包含引号，脚本会自动处理
- 脚本会自动处理NULL值和字符串值
- 如果表结构文件不存在，脚本会跳过该表的处理

import os
import re
import csv
import pandas as pd
from pathlib import Path
import glob

class SQLProcessor:
    """
    SQL文件处理器，用于提取INSERT和UPDATE操作并保存到Excel
    """
    
    def __init__(self, sql_dir=".", table_structure_dir="tableStructure", output_dir="output"):
        """
        初始化SQL处理器
        :param sql_dir: SQL文件目录
        :param table_structure_dir: 表结构CSV文件目录
        :param output_dir: 输出Excel文件目录
        """
        self.sql_dir = Path(sql_dir)
        self.table_structure_dir = Path(table_structure_dir)
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        # 存储表结构信息
        self.table_structures = {}
        self.load_table_structures()
    
    def load_table_structures(self):
        """
        加载表结构信息从CSV文件
        """
        csv_files = glob.glob(str(self.table_structure_dir / "*.csv"))

        for csv_file in csv_files:
            table_name = Path(csv_file).stem  # 获取文件名（不含扩展名）

            try:
                # 直接读取第一行，按逗号分割
                with open(csv_file, 'r', encoding='utf-8') as f:
                    first_line = f.readline().strip()
                    # 按逗号分割并清理字段名
                    headers = [field.strip().strip('"') for field in first_line.split(',')]
                    self.table_structures[table_name] = headers
                    print(f"加载表结构: {table_name} -> {len(headers)} 个字段")
                    print(f"  字段: {headers}")
            except Exception as e:
                print(f"加载表结构失败 {csv_file}: {e}")
    
    def extract_table_operations(self, sql_content, target_table):
        """
        从SQL内容中提取指定表的INSERT和UPDATE操作
        :param sql_content: SQL文件内容
        :param target_table: 目标表名
        :return: 操作列表
        """
        operations = []

        # 匹配INSERT操作的正则表达式
        insert_pattern = rf"### INSERT INTO `[^`]*`\.`{target_table}`\s*\n### SET\s*\n((?:###\s+@\d+=.*\n)*)"

        # 匹配UPDATE操作的正则表达式
        update_pattern = rf"### UPDATE `[^`]*`\.`{target_table}`\s*\n### WHERE\s*\n((?:###\s+@\d+=.*\n)*)### SET\s*\n((?:###\s+@\d+=.*\n)*)"

        # 提取INSERT操作
        insert_matches = re.finditer(insert_pattern, sql_content, re.MULTILINE)
        insert_count = 0
        for match in insert_matches:
            set_clause = match.group(1)
            data = self.parse_set_clause(set_clause)
            operations.append({
                'operation': 'INSERT',
                'table': target_table,
                'data': data
            })
            insert_count += 1

        # 提取UPDATE操作
        update_matches = re.finditer(update_pattern, sql_content, re.MULTILINE)
        update_count = 0
        for match in update_matches:
            where_clause = match.group(1)
            set_clause = match.group(2)
            where_data = self.parse_set_clause(where_clause)
            set_data = self.parse_set_clause(set_clause)
            operations.append({
                'operation': 'UPDATE',
                'table': target_table,
                'where_data': where_data,
                'data': set_data
            })
            update_count += 1

        if insert_count > 0 or update_count > 0:
            print(f"      INSERT: {insert_count}, UPDATE: {update_count}")

        return operations
    
    def parse_set_clause(self, clause):
        """
        解析SET子句，提取字段值
        :param clause: SET子句内容
        :return: 字段值字典
        """
        data = {}
        lines = clause.strip().split('\n')
        
        for line in lines:
            line = line.strip()
            if line.startswith('###'):
                # 匹配 @数字=值 的模式
                match = re.match(r'###\s+@(\d+)=(.*)', line)
                if match:
                    field_index = int(match.group(1)) - 1  # 转换为0基索引
                    value = match.group(2).strip()
                    
                    # 处理NULL值
                    if value == 'NULL':
                        value = None
                    # 处理字符串值（去除引号）
                    elif value.startswith("'") and value.endswith("'"):
                        value = value[1:-1]
                    
                    data[field_index] = value
        
        return data
    

    
    def process_sql_files(self):
        """
        处理所有SQL文件
        """
        sql_files = glob.glob(str(self.sql_dir / "*.sql"))
        tables_with_data = []
        tables_without_data = []

        # 按表名分组处理
        for table_name in self.table_structures.keys():
            print(f"\n处理表: {table_name}")
            all_operations = []

            # 处理每个SQL文件
            for sql_file in sql_files:
                file_name = Path(sql_file).name
                print(f"  处理文件: {file_name}")

                try:
                    with open(sql_file, 'r', encoding='utf-8') as f:
                        sql_content = f.read()

                    operations = self.extract_table_operations(sql_content, table_name)
                    if operations:
                        print(f"    找到 {len(operations)} 个操作")
                        all_operations.extend(operations)

                except Exception as e:
                    print(f"    处理文件失败: {e}")

            # 保存该表的所有操作到Excel
            if all_operations:
                self.save_to_excel(all_operations, table_name)
                print(f"  总共找到 {len(all_operations)} 个操作，已保存到Excel")
                tables_with_data.append(table_name)
            else:
                print(f"  未找到相关操作")
                tables_without_data.append(table_name)

        # 显示汇总信息
        print(f"\n=== 处理汇总 ===")
        print(f"有数据的表 ({len(tables_with_data)}):")
        for table in tables_with_data:
            print(f"  ✓ {table}")

        print(f"\n无数据的表 ({len(tables_without_data)}):")
        for table in tables_without_data:
            print(f"  ✗ {table}")
    
    def save_to_excel(self, operations, table_name):
        """
        将操作数据保存到Excel文件
        :param operations: 操作列表
        :param table_name: 表名
        """
        if not operations:
            return

        # 准备数据
        rows = []
        headers = self.table_structures.get(table_name, [])

        for op in operations:
            # 创建基础行数据，按CSV字段顺序
            row = {'操作类型': op['operation']}

            # 按照CSV中定义的字段顺序添加数据
            if 'data' in op:
                indexed_data = op['data']
                for i, header in enumerate(headers):
                    if i in indexed_data:
                        row[header] = indexed_data[i]
                    else:
                        row[header] = None

            rows.append(row)

        # 创建DataFrame并保存到Excel
        df = pd.DataFrame(rows)
        output_file = self.output_dir / f"{table_name}_operations.xlsx"

        try:
            # 如果文件已存在，直接覆盖
            if output_file.exists():
                print(f"    文件已存在，将覆盖: {output_file}")

            df.to_excel(output_file, index=False, engine='openpyxl')
            print(f"    保存到: {output_file}")
        except Exception as e:
            print(f"    保存Excel失败: {e}")

def main():
    """
    主函数
    """
    processor = SQLProcessor()
    processor.process_sql_files()
    print("\n处理完成！")

if __name__ == "__main__":
    main()

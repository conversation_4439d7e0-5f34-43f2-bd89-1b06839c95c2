#!/usr/bin/env python3
"""
SQL数据处理脚本使用示例
"""

from sql_processor import SQLProcessor

def main():
    """
    运行SQL处理器的示例
    """
    print("=== SQL数据处理脚本 ===")
    print("正在处理SQL文件...")
    
    # 创建处理器实例
    processor = SQLProcessor(
        sql_dir=".",                    # SQL文件目录
        table_structure_dir="tableStructure",  # 表结构CSV目录
        output_dir="output"             # 输出Excel目录
    )
    
    # 处理所有SQL文件
    processor.process_sql_files()
    
    print("\n=== 处理完成 ===")
    print("请查看output目录中的Excel文件")

if __name__ == "__main__":
    main()
